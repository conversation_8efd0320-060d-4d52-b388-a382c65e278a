import {
  Dispatch,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useAtom, useAtomValue } from "jotai";
import { useQueryClient } from "@tanstack/react-query";

import { UserContext } from "common/contexts/UserContext";
import {
  INIT_BUTTERFLY_COUNT,
  INIT_NOTIFICATION_COUNT,
  INIT_TIMELINE_COUNT,
  LOAD_BUTTERFLY_COUNT,
  LOAD_NOTIFICATION_COUNT,
  LOAD_PHOTOS_COUNT,
  LOAD_TIMELINE_COUNT,
} from "common/config";
import {
  fetchProfileActivities,
  getProfilePosts,
  getStoryWithMemories,
  generateMemory,
  getUserAllBots,
  getGenerateCountPerDay,
} from "common/libraries/api";
import { selectedProfileAtom } from "common/state/selectedProfile";
import { useTriggerAtomToSetState } from "common/state/useTriggerAtomToSetState";
import { useProfilePendingPostTasks } from "common/state/useProfilePendingPostTasks";
import { BotsContext } from "common/contexts/BotsContext";
import { useOnTriggerProfilePostsReload } from "common/state/triggerProfilePostsReload";
import { useActionRequiresAuth } from "common/state/useActionRequiresAuth";
import { trackEvent } from "common/utils/trackEvent";
import {
  RealtimeChangesSubscription,
  useRealtimeChanges,
} from "common/contexts/RealtimeChangesContext";
import { useToast } from "common/contexts/ToastContext";
import { isCapacitor } from "common/utils/Helper";
import {
  getItemStorage,
  removeItemStorage,
} from "common/utils/localStorageWrappers";
import {
  makeGeneratePostAtom,
  triggerNewPostAtomFamily,
  triggerPokeAtomFamily,
} from "common/state/generationActions";
import { Icons } from "common/assets/IconLoader";
import { pushPermStatusAtom } from "common/plugins/PushNotificationsContext";
import { useTheme } from "common/contexts/ThemeContext";
import { pendingGenerationActionsAtom } from "common/state/GlobalGenerationActionsContext";

interface UseProfilePostsProps {
  profile: any;
  isLoaded: boolean;
  isPreventRerender?: boolean;
  creator: any;
  setShowPrivate: Dispatch<boolean>;
  following: boolean;
  username: string;
  isFetchPosts: boolean;
  botOtherData: any;
  pokesQuota: any;
  setGenerateCount: Dispatch<number>;
}

const useProfilePosts = ({
  profile,
  isLoaded,
  isPreventRerender,
  creator,
  setShowPrivate,
  following,
  username,
  isFetchPosts,
  botOtherData,
  pokesQuota,
  setGenerateCount,
}: UseProfilePostsProps) => {
  const { selectedProfileId, user, isNewPostCreated, setIsNewPostCreated } =
    useContext(UserContext);
  const selectedProfile = useAtomValue(selectedProfileAtom);
  const { bots, isFetchingBots } = useContext(BotsContext);
  const { onActionRequiresAuth } = useActionRequiresAuth();
  const { trackUpdatingPost, addRealtimeSubscription, untrackUpdatingPost } =
    useRealtimeChanges();
  const { invokeToast } = useToast();
  const pushPermStatus = useAtomValue(pushPermStatusAtom);
  const queryClient = useQueryClient();
  const { theme } = useTheme();
  const pendingGenerationActions = useAtomValue(pendingGenerationActionsAtom);

  const [activities, setActivities] = useState<any[]>([]);
  const [initPostsLoading, setInitPostsLoading] = useState<boolean>(true);
  const [loadedPosts, setLoadedPosts] = useState<any[] | null>(null);
  const [fetchPostsLoading, setFetchPostsLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [postsCursor, setPostsCursor] = useState<string | undefined>(undefined);
  const [stories, setStories] = useState<any[]>();
  const [isPoke, setIsPoke] = useState<boolean>(false);
  const [activityLoading, setActivityLoading] = useState<boolean>(false);
  const [isButterfliesLoading, setIsButterfliesLoading] =
    useState<boolean>(false);
  const [botsCursor, setBotsCursor] = useState<string>("init");
  const [butterflies, setButterflies] = useState<any[]>([]);
  const [deletedBotId, setDeletedBotId] = useState(0);
  const [showPokeModal, setShowPokeModal] = useState<boolean>(false);
  const [isOpenGetNotified, setIsOpenGetNotified] = useState(false);
  const [isLoadingRemainingCreditInfo, setIsLoadingRemainingCreditInfo] =
    useState(false);

  const postsLoadingRef = useRef(false);
  const activityRef = useRef(false);
  const startPokeTimeRef = useRef<number>(0);

  const nonLocalPendingPosts = useMemo(
    () => Object.values(pendingGenerationActions),
    [pendingGenerationActions],
  );

  const profileId = useMemo(() => profile?.id, [profile]);

  const triggerNewPostAtom = useMemo(
    () => triggerNewPostAtomFamily(profileId),
    [profileId],
  );

  const [showNewPostModal, setShowNewPostModal] = useAtom(triggerNewPostAtom);

  const isProfilePublic = useMemo(
    () => profile && profile?.visibility === "public",
    [profile],
  );

  const isMe = useMemo(() => {
    setActiveTab(0);
    return profileId === selectedProfileId;
  }, [selectedProfileId, profileId]);

  const isMyBot = useMemo(
    () => !!creator && creator === selectedProfile?.username,
    [creator, selectedProfile],
  );

  const isProfilePrivate = useMemo(
    () => profile && profile?.visibility === "private",
    [profile],
  );

  const isUser = useMemo(() => user?.id === profile?.user_id, [user, profile]);
  const isHuman = useMemo(() => profile && !!profile?.user_id, [profile]);

  const botInfoFromOwnedBots = useMemo(() => {
    if (!bots) {
      return false;
    }

    const bot = bots.find((bot) => bot.profile_id === profileId);
    return bot;
  }, [bots, profileId]);

  const loadedPostsIds = useMemo(
    () => new Set(loadedPosts ? loadedPosts.map((post) => post.id) : []),
    [loadedPosts],
  );

  const actionRequiresAuthMarketingFocus = useMemo(() => {
    if (!profile) {
      return undefined;
    }

    return {
      profile: profile,
    };
  }, [profile]);

  const botList = useMemo(() => {
    if (deletedBotId) {
      const filteredBots = bots.filter((bot) => bot.id !== deletedBotId);
      return isMe ? filteredBots : butterflies;
    }
    return isMe ? bots : butterflies;
  }, [bots, butterflies, isMe, deletedBotId]);

  const isLoading = useMemo(
    () => (isMe ? isFetchingBots : isButterfliesLoading),
    [isFetchingBots, isButterfliesLoading, isMe],
  );

  const botId = botOtherData?.id;

  const generatePostAtom = useMemo(
    () =>
      makeGeneratePostAtom(
        queryClient,
        selectedProfileId,
        botId,
        profileId,
        "post",
      ),
    [botId, profileId, queryClient, selectedProfileId],
  );
  const generatePostMutation = useAtomValue(generatePostAtom);

  const generatePostWithPromptAtom = useMemo(
    () =>
      makeGeneratePostAtom(
        queryClient,
        selectedProfileId,
        botId,
        profileId,
        "postWithPrompt",
      ),
    [botId, profileId, queryClient, selectedProfileId],
  );
  const generatePostWithPromptMutation = useAtomValue(
    generatePostWithPromptAtom,
  );

  const postCount = (loadedPosts ?? []).length;
  const isUserAnonymous = !user && !selectedProfile;
  const PHOTOS_TAB_INDEX = 0;
  const isOwnedByCurrentProfile = !!botInfoFromOwnedBots;
  let subscriptionPost: RealtimeChangesSubscription;

  const { needsReloadAfterPokeCompleteAtom, pendingPostItemsAtom } =
    useProfilePendingPostTasks(profileId, isOwnedByCurrentProfile, username);
  // Helper transition from atom signal to local component state for reloading after a poke is completed
  useTriggerAtomToSetState(needsReloadAfterPokeCompleteAtom, setIsPoke);
  const pendingPostItems = useAtomValue(pendingPostItemsAtom);

  const filteredPendingPosts = useMemo(() => {
    if (!pendingPostItems) return [];

    return pendingPostItems
      .filter((pendingPostItem) => {
        return (
          pendingPostItem.localInfo ||
          !loadedPostsIds.has(pendingPostItem.post.id)
        );
      })
      .map((pendingPostItem) => {
        const { post, ...rest } = pendingPostItem;
        return { ...rest, ...post };
      });
  }, [loadedPostsIds, pendingPostItems]);

  const renderPosts = useMemo(() => {
    if (filteredPendingPosts.length === 0 && loadedPosts === null) {
      return null;
    }

    return [...filteredPendingPosts, ...(loadedPosts ?? [])];
  }, [filteredPendingPosts, loadedPosts]);

  // Helper transition from atom signal to local component state for
  // the Poke modal.
  const triggerPokeAtom = useMemo(
    () => triggerPokeAtomFamily(profileId),
    [profileId],
  );
  useTriggerAtomToSetState(triggerPokeAtom, setShowPokeModal);

  useOnTriggerProfilePostsReload(
    useCallback(() => {
      // Early return if no profileId is available
      if (!profileId) return;

      // Reset to photos tab and refresh data
      setActiveTab(PHOTOS_TAB_INDEX);
      fetchData();
    }, [profileId]), // Dependency managed via closure
    { profileId },
  );

  const fetchPosts = useCallback(
    async (_existingRenderPosts = [], _cursor = "init") => {
      // Early return if the component is not loaded
      if (!isLoaded) return;

      // Prevent concurrent fetches
      if (postsLoadingRef.current) return;

      postsLoadingRef.current = true;
      setFetchPostsLoading(true);

      try {
        const fetchingCount = !postCount
          ? INIT_TIMELINE_COUNT
          : activeTab === 0
            ? LOAD_TIMELINE_COUNT
            : LOAD_PHOTOS_COUNT;

        if (!_cursor) return;
        // Fetch posts from the API
        const response = await getProfilePosts(
          profile?.id,
          fetchingCount,
          _cursor,
          selectedProfileId,
        );

        const { data: postData, page_info, isPrivate, error } = response?.data;

        if (error) {
          throw error;
        }

        if (isPrivate) {
          setShowPrivate(true);
          return;
        } else {
          setShowPrivate(false);
        }

        // Update the cursor for the next page
        const nextCursor = page_info?.next_cursor;
        setPostsCursor(nextCursor);

        if (postData) {
          setLoadedPosts((currentPosts) => {
            if (currentPosts === null) {
              return postData ?? [];
            }

            const originIdSet = new Set(currentPosts.map((post) => post.id));
            const uniqueNewPosts =
              postData?.filter((post: any) => !originIdSet.has(post.id)) ?? [];

            return [...currentPosts, ...uniqueNewPosts];
          });
        }
      } catch (err) {
        console.error("Fetch operation failed:", err);
        return;
      } finally {
        setFetchPostsLoading(false);
        postsLoadingRef.current = false;
      }
    },
    [
      isLoaded,
      profile,
      isProfilePublic,
      isMe,
      isMyBot,
      following,
      isProfilePrivate,
      selectedProfile,
      postCount,
      activeTab,
      setShowPrivate,
      isUser,
    ],
  );

  const fetchPostsInitial = useCallback(async () => {
    return fetchPosts([]);
  }, [fetchPosts]);

  useEffect(() => {
    if (isCapacitor()) return;

    const deletedBotId = getItemStorage("deleted_bot_id");
    if (deletedBotId) {
      setDeletedBotId(Number(deletedBotId));
      removeItemStorage("deleted_bot_id");
    }
  }, []);

  useEffect(() => {
    // Skip effect if required dependencies are missing or rerender prevention is active
    if (!profile || !isLoaded || isPreventRerender) {
      return;
    }

    const initializeProfileData = async () => {
      try {
        setActivities([]); // Reset activities state

        // Execute fetch operations concurrently for better performance
        await Promise.all([fetchData(), fetchStories()]);
      } catch (error) {
        console.error("Failed to initialize profile data:", error);
      }
    };

    initializeProfileData();
  }, [profileId, creator, selectedProfileId, isLoaded]);

  // Reload after a creating a new human post is completed
  useEffect(() => {
    if (isHuman && isNewPostCreated) {
      setActiveTab(PHOTOS_TAB_INDEX);
      fetchData();
      setIsNewPostCreated(false);
    }
  }, [isHuman, isNewPostCreated]);

  // Reload after a poke (post regeneration) is completed
  useEffect(() => {
    if (isPoke && !isHuman) {
      setActiveTab(0);
      fetchData();
      setIsPoke(false);
    }
  }, [isPoke]);

  useEffect(() => {
    if (username && !isPreventRerender) {
      setInitPostsLoading(true);
      setLoadedPosts(null);
    }
  }, [username, isFetchPosts]);

  useEffect(() => {
    if (isHuman && !isMe) {
      fetchInitButterflies();
    }
  }, [isHuman, profile, selectedProfile]);

  const fetchActivities = async (): Promise<void> => {
    // Guard clauses for early returns
    if (activityRef.current || !selectedProfile?.id || !profile?.id) {
      return;
    }

    try {
      // Set loading state and lock
      activityRef.current = true;
      setActivityLoading(true);

      // Calculate pagination parameters
      const count =
        activities.length > 0
          ? LOAD_NOTIFICATION_COUNT
          : INIT_NOTIFICATION_COUNT;
      const rangeStart = activities.length;
      const rangeEnd = rangeStart + count - 1;

      // Fetch data
      const { data } = await fetchProfileActivities({
        profileId: profile.id,
        rangeStart,
        rangeEnd,
      });

      // Update state with new activities if data exists
      if (data?.length) {
        setActivities((prevActivities) => [...prevActivities, ...data]);
      }
    } catch (error) {
      console.error("Failed to fetch activities:", error);
      // Could add error handling/toast notification here
    } finally {
      // Reset loading states
      setActivityLoading(false);
      activityRef.current = false;
    }
  };

  useEffect(() => {
    if (!isHuman && activeTab == 1) {
      fetchActivities();
    }
  }, [isHuman, activeTab]);

  const fetchData = useCallback(async () => {
    // Prevent concurrent fetches
    if (postsLoadingRef.current) return;

    setInitPostsLoading(true);
    setLoadedPosts(null);
    await fetchPosts([]);
    setInitPostsLoading(false);
  }, [fetchPosts]);

  const fetchStories = async (): Promise<void> => {
    // Early return for anonymous users
    if (isUserAnonymous) {
      setStories([]);
      return;
    }

    try {
      // Fetch stories data
      const {
        data: { data: storiesData },
      } = await getStoryWithMemories(profile.id, selectedProfile?.id, isMyBot);

      // Process and set stories
      if (Array.isArray(storiesData) && storiesData.length > 0) {
        const filteredStories = storiesData.filter(
          (story: { memories: unknown[] }) =>
            isMyBot || story.memories.length > 0,
        );
        setStories(filteredStories);
      } else {
        setStories([]);
      }
    } catch (error) {
      console.error("Failed to fetch stories:", error);
      setStories([]);
    }
  };

  const fetchPostsNext = useCallback(async () => {
    if (renderPosts === null) return;

    if (isUserAnonymous) {
      if (renderPosts.length > 15) {
        onActionRequiresAuth({
          action: "view_more_posts",
          source: "profile",
          linkPath: undefined,
          marketingFocus: actionRequiresAuthMarketingFocus,
        });
        return;
      }
    }
    return fetchPosts(renderPosts, postsCursor);
  }, [
    fetchPosts,
    isUserAnonymous,
    onActionRequiresAuth,
    postsCursor,
    renderPosts,
  ]);

  type MemoryData = {
    description: string;
  };

  type GenerateMemoryResponse = {
    data?: {
      post_id?: number;
    };
  };

  const handleAdd = useCallback(
    async (memoryData: MemoryData): Promise<void> => {
      if (!isMyBot || !botOtherData) {
        return;
      }

      trackEvent("add_story");

      try {
        const { data: result } = (await generateMemory({
          context: memoryData.description,
          bot_id: botOtherData.id,
        })) as { data: GenerateMemoryResponse };

        const updatingPostId = result?.data?.post_id;

        if (!updatingPostId) {
          console.warn("generateMemory returned no post_id to observe");
          return;
        }

        trackUpdatingPost(updatingPostId);

        // Subscribe to real-time post updates
        subscriptionPost = addRealtimeSubscription(
          `story-post-channel-${updatingPostId}`,
          "UPDATE",
          "posts",
          async (payload) => {
            if (payload.new?.visibility === "public") {
              // Cleanup subscriptions
              subscriptionPost.unsubscribe();
              untrackUpdatingPost(updatingPostId);

              // Update UI
              await Promise.all([fetchStories(), fetchData()]);
              setActiveTab(0);
            }
          },
        );
      } catch (error) {
        console.error("Failed to add story:", error);
        invokeToast({
          type: "error",
          text: "Failed to add story. Please try again later.",
          position: "top",
        });
      } finally {
        // Ensure stories are fetched even if there was an error
        await fetchStories();
      }
    },
    [
      isMyBot,
      botOtherData,
      trackUpdatingPost,
      addRealtimeSubscription,
      untrackUpdatingPost,
      fetchStories,
      fetchData,
      setActiveTab,
      invokeToast,
    ],
  );

  const fetchButterflies = async (isInit?: boolean) => {
    if (isMe) return;

    const fetchingCount = isInit ? INIT_BUTTERFLY_COUNT : LOAD_BUTTERFLY_COUNT;

    try {
      if (!botsCursor) return;
      const response = await getUserAllBots(
        profile?.id,
        isInit ? "init" : botsCursor,
        fetchingCount,
      );
      const { data, page_info, error } = response.data;

      if (error) {
        throw error;
      }

      // Update the cursor for the next page
      const nextCursor = page_info?.next_cursor;
      setBotsCursor(nextCursor);

      if (data) {
        const originIdSet = new Set(
          (isInit ? [] : butterflies)?.map((bot) => bot.id),
        );
        const uniqueNewBots =
          data?.filter((bot: any) => !originIdSet.has(bot.id)) ?? [];

        setButterflies((currentButterflis) => [
          ...(isInit ? [] : currentButterflis),
          ...uniqueNewBots,
        ]);
      }
    } catch (error) {
      console.error("Error fetching bots:", error);
      return;
    }
  };

  const fetchInitButterflies = useCallback(async () => {
    try {
      setIsButterfliesLoading(true);
      await fetchButterflies(true);
    } catch (error) {
      console.error("Failed to fetch initial butterflies:", error);
    } finally {
      setIsButterfliesLoading(false);
    }
  }, [profile]);

  const handleGeneratePost = useCallback(
    async ({
      type,
      promptText,
      taggedProfileIds = [],
      justCameFromMCIModal = false,
    }: HandleGeneratePostParams): Promise<void> => {
      // Check for frequency limit and show toast if needed
      if (nonLocalPendingPosts.length === 3) {
        const now = new Date();
        const thirtyMinutesAgo = new Date(now.getTime() - 30 * 60 * 1000);

        // Check if all 3 posts have startedAtDate older than 30 minutes
        const allPostsOlderThan30Minutes = nonLocalPendingPosts.every(
          (post) => {
            if (!post.startedAtDate) return false;
            return post.startedAtDate < thirtyMinutesAgo;
          },
        );

        if (allPostsOlderThan30Minutes) {
          invokeToast({
            type: "common",
            text: "Too frequent pokes. Please try again in 30 minutes.",
            position: "top",
            header: "Rate Limit",
            style: "fill",
            icon: theme === "dark" ? Icons.WarningDarkIcon : Icons.WarningIcon,
          });
          return; // Exit early to prevent the poke
        }
      }

      const logEventProperties = {
        type,
        has_prompt_text: Boolean(promptText?.trim()),
        has_tagged_profiles: taggedProfileIds.length > 0,
        tagged_profile_ids: taggedProfileIds,
        from_mci_modal: justCameFromMCIModal,
      };

      try {
        // Log event and close modals
        console.log("Starting poke...", logEventProperties);
        trackEvent("start_poke", logEventProperties);

        // Close appropriate modal
        if (type === "poke") {
          setShowPokeModal(false);
        } else {
          setShowNewPostModal(false);
        }

        // Show generating toast notification
        invokeToast({
          type: "dark",
          text: "Generating new post",
          position: "top",
          style: "fit",
          icon: Icons.ImageIcon2,
        });

        // Handle push notification prompt
        if (
          !selectedProfile?.users?.opened_first_post_share_modal &&
          pushPermStatus === "prompt"
        ) {
          setTimeout(() => setIsOpenGetNotified(true), 5000);
        }

        // Generate post based on type
        if (type === "poke" || !promptText?.trim()) {
          await generatePostMutation.mutateAsync({
            userPostDescription: undefined,
            taggedProfileIds: undefined,
          });
        } else {
          await generatePostWithPromptMutation.mutateAsync({
            userPostDescription: promptText,
            taggedProfileIds,
          });
        }

        // Record generation start time
        startPokeTimeRef.current = Date.now();
      } catch (error) {
        // Handle errors
        console.error("Error generating post:", error);

        invokeToast({
          type: "common",
          text: "Sorry, seems like there's an issue",
          position: "top",
          header: "Error Generating Post",
          style: "fill",
          icon: theme === "dark" ? Icons.WarningDarkIcon : Icons.WarningIcon,
        });

        // Close modals on error
        setShowPokeModal(false);
        setShowNewPostModal(false);
      }
    },
    [
      generatePostMutation,
      generatePostWithPromptMutation,
      invokeToast,
      pushPermStatus,
      selectedProfile?.users?.opened_first_post_share_modal,
      setIsOpenGetNotified,
      setShowNewPostModal,
      setShowPokeModal,
      theme,
      trackEvent,
    ],
  );

  /**
   * Checks if the user has exceeded their daily generation limit
   * @returns Promise<boolean> - Returns true if user is within limits, false otherwise
   */
  const checkLimit = async (): Promise<boolean> => {
    // Early return if required data is missing
    if (!selectedProfile?.id || !botOtherData?.id) {
      console.warn("Missing required profile or bot data for limit check");
      return false;
    }

    try {
      setIsLoadingRemainingCreditInfo(true);

      const { data } = await getGenerateCountPerDay({
        type: "new",
        profile_id: selectedProfile.id,
        target_id: botOtherData.id,
      });

      // Calculate and set the current generation count, capped at quota
      const currentCount = Math.min(data.count, pokesQuota);
      setGenerateCount(currentCount);

      return data.count < pokesQuota;
    } catch (error) {
      console.error("Failed to check generation limit:", error);
      return false;
    } finally {
      setIsLoadingRemainingCreditInfo(false);
    }
  };

  const handleClickPokeIcon = () => {
    checkLimit();
    trackEvent("profile_poke");
    setShowPokeModal(true);
  }; // Added checkLimit as dependency since it's used inside

  return {
    activeTab,
    fetchData,
    stories,
    setActiveTab,
    initPostsLoading,
    renderPosts,
    fetchPostsNext,
    fetchPostsLoading,
    activities,
    activityLoading,
    fetchActivities,
    handleAdd,
    setDeletedBotId,
    isLoading,
    botList,
    fetchButterflies,
    handleClickPokeIcon,
    showPokeModal,
    setShowPokeModal,
    handleGeneratePost,
    isLoadingRemainingCreditInfo,
    isOpenGetNotified,
    setIsOpenGetNotified,
  };
};

export default useProfilePosts;
