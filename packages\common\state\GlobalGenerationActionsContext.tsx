"use client";
import { PendingPostDataItemWithLocal } from "common/state/generationActions";
import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  FC,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";
import safeStringify from "fast-safe-stringify";
import { useQueryClient } from "@tanstack/react-query";
import { usePreviousDistinct } from "react-use";

import {
  RealtimeChangesSubscription,
  useRealtimeChanges,
} from "common/contexts/RealtimeChangesContext";
import { useToast } from "common/contexts/ToastContext";
import { makeProfilePendingTasksQueryKey } from "common/state/profilePendingTasks";
import { trackEvent } from "common/utils/trackEvent";
import { useGlobalRouter } from "common/utils/useGlobalRouter";
import { selectedProfileAtom } from "common/state/selectedProfile";
import {
  getGenerationStatusV3,
  getPostWithFirstComment,
} from "common/libraries/api";
import { useAppState } from "common/plugins/AppStateContext";
import supabase from "common/libraries/supabaseClient";
import { wrappedError } from "common/utils/errorUtils";
import { captureError } from "common/utils/sentryWrapper";
import { useFirstPostShareModal } from "common/state/useFirstPostShareModal";
import { isCapacitor } from "common/utils/Helper";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import { shouldFetchBotsAtom } from "common/contexts/BotsContext";

const __SHOULD_LOG = false;
const __log = (...args: any) => {
  if (!__SHOULD_LOG) {
    return;
  }

  console.log(...args);
};

// TODO: once this handles more than just pending posts on profiles, this type will have to change
export const pendingGenerationActionsAtom = atom<
  Record<number, PendingPostDataItemWithLocal>
>({});

// TODO: include in pendingGenerationActionsAtom instead of tracking separately?
const taskAssociatedMetadataAtom = atom<Record<number, AssociatedMetadata>>({});

const completedGenerationActionsAtom = atom<
  Record<number, PendingPostDataItemWithLocal>
>({});

interface AssociatedMetadata {
  targetProfileId: number;
  targetProfileUsername: string;
}

interface GenerationActionsCtx {
  pendingGenerationActionsAtom: typeof pendingGenerationActionsAtom;
  handlePendingGenerationActions: (
    pendingTaskItems: PendingPostDataItemWithLocal[],
    associatedMetadata: AssociatedMetadata,
  ) => void;
  completedGenerationActionsAtom: typeof completedGenerationActionsAtom;
}

const _generationActionsContext = {
  pendingGenerationActionsAtom,
  handlePendingGenerationActions: (
    pendingTaskItems: PendingPostDataItemWithLocal[],
    associatedMetadata: AssociatedMetadata,
  ) => {},
  completedGenerationActionsAtom,
};

const GlobalGenerationActionsContext = createContext<GenerationActionsCtx>(
  _generationActionsContext,
);

export const useGlobalGenerationActions = () => {
  const context = useContext(GlobalGenerationActionsContext);
  return context;
};

interface GlobalGenerationActionsContextProviderProps {
  children?: any;
}

const fetchPostPublishData = async ({
  postId,
  shouldOpenFirstPostModal,
}: {
  postId: number;
  shouldOpenFirstPostModal: boolean | undefined;
}): Promise<any> => {
  try {
    const response = !shouldOpenFirstPostModal
      ? await supabase
          .from("posts")
          .select(
            `
            id, created_at, profile_id, media_url, slug, type, visibility, nsfw, nsfl,
            tagged_profile_ids
          `,
          )
          .eq("id", postId)
          .maybeSingle()
      : (await getPostWithFirstComment(postId)).data;

    const { data, error: supabaseError } = response;

    if (supabaseError) {
      throw wrappedError(supabaseError);
    }

    return data;
  } catch (error) {
    console.error("Failed to load post publish data", error);
    throw error;
  }
};

export const GlobalGenerationActionsContextProvider: FC<
  GlobalGenerationActionsContextProviderProps
> = ({ children }) => {
  const [pendingGenerationActions, setPendingGenerationActions] = useAtom(
    pendingGenerationActionsAtom,
  );
  const [taskAssociatedMetadata, setTaskAssociatedMetadata] = useAtom(
    taskAssociatedMetadataAtom,
  );
  const [, setCompletedGenerationActions] = useAtom(
    completedGenerationActionsAtom,
  );

  const handlePendingGenerationActions = useCallback(
    (
      incomingItems: PendingPostDataItemWithLocal[],
      associatedMetadata: AssociatedMetadata,
    ) => {
      __log("!!! handlePendingGenerationActions called with:", incomingItems);

      const alreadyHaveAllTasks = incomingItems.every(
        (item) => pendingGenerationActions[item.task.id],
      );
      if (alreadyHaveAllTasks) {
        // we already have info on all of these tasks
        __log(
          "!!! handlePendingGenerationActions - we already have info on all of these tasks",
        );
        return;
      }

      setPendingGenerationActions((prev) => {
        const next = {
          ...prev,
        };

        incomingItems.forEach((item) => {
          if (prev[item.task.id]) {
            // we already have info on this task
            return;
          }
          next[item.task.id] = item;
        });

        __log(
          "!!! handlePendingGenerationActions - setPendingGenerationActions - setting next:",
          next,
        );
        return next;
      });

      setTaskAssociatedMetadata((prev) => {
        const next = {
          ...prev,
        };

        incomingItems.forEach((item) => {
          next[item.task.id] = associatedMetadata;
        });

        return next;
      });
    },
    [
      pendingGenerationActions,
      setPendingGenerationActions,
      setTaskAssociatedMetadata,
    ],
  );

  const selectedProfile = useAtomValue(selectedProfileAtom);
  const { setFirstPostShareModal } = useFirstPostShareModal();
  const setShouldFetchBots = useSetAtom(shouldFetchBotsAtom);

  const shouldOpenFirstPostModal =
    useFeatureFlagEnabled("FIRST_POST_SHARE_MODAL") &&
    isCapacitor() &&
    !selectedProfile?.users?.opened_first_post_share_modal;

  ////////////////////////////////////////////////
  //
  // Clear out state when selected profile changes
  // (alternatively we could make sure these state atoms are created dynamically based on the selected profile)
  const previousSelectedProfile = usePreviousDistinct(selectedProfile);
  useEffect(() => {
    if (!previousSelectedProfile) {
      return;
    }

    if (selectedProfile === previousSelectedProfile) {
      return;
    }

    setCompletedGenerationActions({});
    setPendingGenerationActions({});
    setTaskAssociatedMetadata({});
  }, [
    selectedProfile,
    previousSelectedProfile,
    setCompletedGenerationActions,
    setPendingGenerationActions,
    setTaskAssociatedMetadata,
  ]);
  /////////////////////////////////////////////////

  const { trackUpdatingPost, untrackUpdatingPost, addRealtimeSubscription } =
    useRealtimeChanges();

  const { invokeToast, showErrorToast } = useToast();

  const router = useGlobalRouter();

  const queryClient = useQueryClient();

  const nonLocalPendingPosts = useMemo(
    () => Object.values(pendingGenerationActions),
    [pendingGenerationActions],
  );
  const pendingGenerationActionsRef = useRef(pendingGenerationActions);
  pendingGenerationActionsRef.current = pendingGenerationActions;

  const cleanUpPendingPostTask = useCallback(
    ({ pendingPost }: { pendingPost: PendingPostDataItemWithLocal }) => {
      const currentPendingPost =
        pendingGenerationActionsRef.current[pendingPost.task.id];
      if (!currentPendingPost) {
        console.warn(
          "cleanUpPendingPostTask - no currentPendingPost found",
          pendingPost,
          pendingGenerationActionsRef.current,
        );
        // returning false here to avoid presenting the toast twice, if the two mechanisms
        // find that it completed at the same time.
        return false;
      }

      const postId = pendingPost.post.id;
      const taskId = pendingPost.task.id;
      const targetProfileId = pendingPost.targetProfileId;

      // Update 'profilePendingTasks' query cache
      queryClient.setQueryData<PendingPostDataItemWithLocal[]>(
        makeProfilePendingTasksQueryKey({ targetProfileId }),
        (old = []) => old.filter((item) => item.post.id !== postId),
      );

      setPendingGenerationActions((prev) => {
        const next = {
          ...prev,
        };

        delete next[taskId];

        __log(
          "!!! cleanUpPendingPostTask - setPendingGenerationActions - setting next:",
          safeStringify(next),
        );
        return next;
      });

      setTaskAssociatedMetadata((prev) => {
        const next = {
          ...prev,
        };

        delete next[taskId];

        return next;
      });

      setCompletedGenerationActions((prev) => {
        const next = {
          ...prev,
        };

        next[taskId] = pendingPost;

        __log(
          "!!! cleanUpPendingPostTask - setCompletedGenerationActions - setting next:",
          safeStringify(next),
        );
        return next;
      });

      return true;
    },
    [
      queryClient,
      setCompletedGenerationActions,
      setPendingGenerationActions,
      setTaskAssociatedMetadata,
    ],
  );

  const presentToastForGeneratedPost = useCallback(
    ({
      targetProfileUsername,
      post,
    }: {
      targetProfileUsername: string;
      post: any;
    }) => {
      invokeToast({
        type: "common",
        text: `${targetProfileUsername}'s new post is up, check it out`,
        position: "top",
        header: "New Post",
        style: "fill",
        icon: "",
        onToastClick: () => {
          let path;
          if (!post.slug || post.slug === "null" || post.slug === "undefined") {
            console.warn(
              "post.slug is missing, falling back to linking to profile",
            );
            path = `/users/${targetProfileUsername}`;
          } else {
            path = `/users/${targetProfileUsername}/p/${post.slug}`;
          }
          router.push(path);
        },
        previewImage: {
          imageUrl: post?.media_url,
          type: "post",
        },
      });
    },
    [
      invokeToast,
      // router, // INTENTIONALLY removed router from dependencies.
    ],
  );

  const presentToastForFailedPost = useCallback(
    ({
      targetProfileUsername,
      reason,
    }: {
      targetProfileUsername: string;
      reason:
        | "task_failed"
        | "post_not_found"
        | "post_hidden"
        | "unexpected_pending_post_error";
    }) => {
      let title = "Error Generating Post";
      let text = "Sorry, something went wrong";
      switch (reason) {
        case "task_failed":
          break;
        case "post_not_found":
          break;
        case "unexpected_pending_post_error":
          break;
        case "post_hidden":
          title = "Beep Boop Bop";
          text = "Post failed to meet our content guidelines";
          break;
      }
      // FIXME: show error toast
      showErrorToast({
        title,
        text,
        onToastClick: () => {
          const path = `/users/${targetProfileUsername}`;
          router.push(path);
        },
      });
    },
    [
      showErrorToast,
      // router, // INTENTIONALLY removed router from dependencies.
    ],
  );

  const handleFailedOrCompletedGenerationTask = useCallback(
    ({
      pendingPost,
      targetProfileUsername,
      taskStatus,
      post,
    }: {
      pendingPost: PendingPostDataItemWithLocal;
      targetProfileUsername: string;
      taskStatus: "failed" | "completed";
      post:
        | null
        | undefined
        | {
            id: number;
            slug: string;
            media_url: string | undefined;
            visibility: "hidden" | "public";
          };
    }) => {
      const stringifiedPendingPost = safeStringify(pendingPost);

      __log(`!!! calling cleanUpPendingPostTask`, stringifiedPendingPost);
      const cleanedUp = cleanUpPendingPostTask({ pendingPost });
      if (!cleanedUp) {
        __log(
          `!!! cleanUpPendingPostTask returned false`,
          stringifiedPendingPost,
        );
        return;
      }

      if (taskStatus === "failed") {
        console.error(
          "Pending post failed - task failed",
          stringifiedPendingPost,
        );
        presentToastForFailedPost({
          targetProfileUsername,
          reason: "task_failed",
        });
        return;
      }

      if (!post) {
        console.error(
          "Pending post failed - post not found",
          stringifiedPendingPost,
        );
        presentToastForFailedPost({
          targetProfileUsername,
          reason: "post_not_found",
        });
        return;
      }

      if (post.visibility === "hidden") {
        console.error(
          "Pending post failed - post hidden",
          stringifiedPendingPost,
        );
        presentToastForFailedPost({
          targetProfileUsername,
          reason: "post_hidden",
        });
        return;
      }

      if (taskStatus === "completed" && post?.visibility === "public") {
        setShouldFetchBots(true);

        if (shouldOpenFirstPostModal) {
          setFirstPostShareModal({
            isOpen: true,
            post,
            presentationReason: "first_post",
          });
        } else {
          presentToastForGeneratedPost({
            targetProfileUsername,
            post,
          });
        }
      } else {
        // TODO: present error toast if we know that the generation task failed?
        console.error("Unexpected pending post state", stringifiedPendingPost);
        presentToastForFailedPost({
          targetProfileUsername,
          reason: "unexpected_pending_post_error",
        });

        captureError("Unexpected pending post state");
      }
    },
    [
      cleanUpPendingPostTask,
      presentToastForFailedPost,
      presentToastForGeneratedPost,
      selectedProfile,
      setFirstPostShareModal,
    ],
  );

  // FIXME: This is awful... Instead of this we should probably have an endpoint that we can poll with a batch of task ids
  const refetchAllPendingTasksStatus = useCallback(
    async (nonLocalPendingPosts: PendingPostDataItemWithLocal[]) => {
      __log("!!! refetchAllPendingTasksStatus");
      const promises = await Promise.allSettled(
        nonLocalPendingPosts.map(async (pendingPost) => {
          const getGenerationStatusResult = await getGenerationStatusV3({
            task_id: pendingPost.task.id,
          });
          return {
            pendingPost,
            taskStatusInfo: getGenerationStatusResult.data,
          };
        }),
      );

      const fulfilledResults = promises
        .filter((promise) => promise.status === "fulfilled")
        .map((promise) => promise.value);

      if (fulfilledResults.length) {
        __log(
          "!!! fulfilledResults:",
          fulfilledResults.map((r) => safeStringify(r)),
        );
      }
      const completedTasks = fulfilledResults.filter(
        (result) =>
          result.taskStatusInfo.status === "completed" ||
          result.taskStatusInfo.status === "failed",
      );
      if (completedTasks.length) {
        __log(
          "!!! found completed or failed tasks:",
          safeStringify(completedTasks),
        );
      }
      for await (const { pendingPost, taskStatusInfo } of completedTasks) {
        // for failed tasks, taskStatusInfo.img_uris will be missing
        let media_url;
        if (taskStatusInfo.img_uris) {
          media_url = taskStatusInfo.img_uris[0]?.original;
        } else {
          media_url = undefined;
        }

        const taskStatus = taskStatusInfo.status;
        const targetProfileUsername =
          taskAssociatedMetadata[pendingPost.task.id].targetProfileUsername;

        // TODO: remove this after completing investigation of tapping on an in-app toast that takes you to /users/$USERNAME/p/undefined
        console.log(
          "!!! Detected completed pending task",
          "refetch",
          pendingPost.task.id,
          taskStatus,
          targetProfileUsername,
          pendingPost.post.id,
          (pendingPost.post as any).slug,
          media_url,
        );

        try {
          const postPublishData = await fetchPostPublishData({
            postId: pendingPost.post.id,
            shouldOpenFirstPostModal,
          });

          if (
            taskStatus === "completed" &&
            postPublishData &&
            postPublishData.visibility !== "public" &&
            postPublishData.visibility !== "hidden"
          ) {
            __log(
              "!!! task completed, post exists, but post is not public yet",
              postPublishData,
            );
            // task completed, post exists, but post is not public yet and hasn't been moderated yet
            return; // continue to next pending task
          }

          if (!postPublishData) {
            console.error(
              "Task failed or completed, but post does not exist",
              pendingPost,
            );
          }

          handleFailedOrCompletedGenerationTask({
            pendingPost,
            taskStatus,
            targetProfileUsername,
            post: postPublishData,
          });
        } catch (error) {
          console.error("Failed to fetch post publish data", error);
        }
      }
    },
    [handleFailedOrCompletedGenerationTask, taskAssociatedMetadata],
  );

  const { appStateAtom } = useAppState();
  const appState = useAtomValue(appStateAtom);
  const isAppActive = appState.isActive;

  useEffect(() => {
    if (!selectedProfile) {
      return;
    }
    if (!nonLocalPendingPosts.length) {
      return;
    }
    if (!isAppActive) {
      return;
    }

    refetchAllPendingTasksStatus(nonLocalPendingPosts);
  }, [
    isAppActive,
    nonLocalPendingPosts,
    refetchAllPendingTasksStatus,
    selectedProfile,
  ]);

  useEffect(() => {
    if (!nonLocalPendingPosts) {
      return;
    }

    // const postIds = nonLocalPendingPosts.map((item) => item.post.id);
    nonLocalPendingPosts.forEach((pendingPost) => {
      __log("!!! trackUpdatingPost", pendingPost.post.id);
      trackUpdatingPost(pendingPost.post.id);
    });

    const subscriptions: RealtimeChangesSubscription[] = [];

    nonLocalPendingPosts.forEach((pendingPost) => {
      const postId = pendingPost.post.id;
      const taskId = pendingPost.task.id;
      const associatedMetadata = taskAssociatedMetadata[taskId];
      __log("!!! setting up a subscription for postId:", postId);
      __log("!!! pendingPost:", { ...pendingPost, postId, taskId });
      __log("!!! associatedMetadata:", associatedMetadata);

      const { targetProfileId, targetProfileUsername } =
        taskAssociatedMetadata[taskId];

      // TODO: add timeout fallback logic in case the task doesn't complete in a reasonable time (5 minutes?)

      const subscription = addRealtimeSubscription(
        `post-channel-${postId}`,
        "UPDATE",
        "posts",
        async (payload) => {
          if (
            (payload.new.visibility !== "public" &&
              payload.new.visibility !== "hidden") ||
            postId !== payload.new.id
          ) {
            return;
          }
          const postPayloadWithoutEmbedding = {
            ...payload.new,
          };
          delete postPayloadWithoutEmbedding.embedding;
          __log(
            `!!! post-channel-${postId} UPDATE with visibility hidden or public`,
            postPayloadWithoutEmbedding,
          );
          const startedAtDate = pendingPost.startedAtDate;
          if (startedAtDate) {
            const now = Date.now();
            const elapsedTime = Math.floor(
              (now - startedAtDate.getTime()) / 1000,
            );
            trackEvent("poke_duration", {
              duration: elapsedTime,
            });
            __log(`!!! poke_duration event logged: ${elapsedTime} seconds`);
          } else {
            console.warn(
              "Can't log 'poke_duration' event, we don't know the start time locally",
            );
          }

          __log("!!! untrackUpdatingPost", postId);
          untrackUpdatingPost(postId);
          __log(`!!! removing subscription for postId;`, postId);
          subscription.unsubscribe();

          let post = payload?.new;

          if (shouldOpenFirstPostModal) {
            try {
              const response = await getPostWithFirstComment(postId);
              const { data, error: responseError } = response.data;

              if (responseError) {
                throw wrappedError(responseError);
              }

              post = data;
            } catch (error) {
              console.error("Failed to load post publish data", error);
              throw error;
            }
          }

          // TODO: remove this after completing investigation of tapping on an in-app toast that takes you to /users/$USERNAME/p/undefined
          console.log(
            "!!! Detected completed pending task",
            "realtime",
            pendingPost.task.id,
            "completed",
            targetProfileUsername,
            post.id,
            post.slug,
            post.media_url,
          );
          handleFailedOrCompletedGenerationTask({
            pendingPost,
            targetProfileUsername,
            taskStatus: "completed",
            post,
          });
        },
      );
      subscriptions.push(subscription);
    });

    return () => {
      nonLocalPendingPosts.forEach((pendingPost) => {
        untrackUpdatingPost(pendingPost.post.id);
      });
      subscriptions.forEach((subscription) => {
        subscription.unsubscribe();
      });
    };
  }, [
    addRealtimeSubscription,
    handleFailedOrCompletedGenerationTask,
    nonLocalPendingPosts,
    taskAssociatedMetadata,
    trackUpdatingPost,
    untrackUpdatingPost,
  ]);

  const ctx = useMemo(() => {
    return {
      pendingGenerationActionsAtom,
      handlePendingGenerationActions,
      completedGenerationActionsAtom,
    };
  }, [handlePendingGenerationActions]);

  return (
    <GlobalGenerationActionsContext.Provider value={ctx}>
      {children}
    </GlobalGenerationActionsContext.Provider>
  );
};
